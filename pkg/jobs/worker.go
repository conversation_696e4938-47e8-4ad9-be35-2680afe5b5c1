package jobs

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/agent-summurizer/pkg/database"
	"github.com/agent-summurizer/pkg/dtos"
	"github.com/agent-summurizer/pkg/entities"
	"github.com/agent-summurizer/pkg/openai"
	"github.com/agent-summurizer/pkg/prompt"
)

func StartWorker(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			fmt.Println("Worker kapanıyor...")
			return
		default:
			chain, err := fetchChain()
			if err != nil {
				time.Sleep(2 * time.Second)
				continue
			}
			if chain == nil {
				time.Sleep(2 * time.Second)
				continue
			}
			sendToOpenAI(chain)
			// _, _ = db.Exec("UPDATE jobs SET status='done', result=$1 WHERE id=$2", result, job.ID)
		}
	}
}

func fetchChain() (*dtos.RequestForChain, error) {
	db := database.DBClient()
	tx := db.Begin()
	var (
		handler                   entities.Handler
		chains                    []entities.Chain
		function_info_for_handler dtos.FunctionInfo
		function_info_for_calls   []dtos.FunctionInfo
		request_for_chain         dtos.RequestForChain
	)
	if err := tx.Model(&entities.Handler{}).
		Where("is_processed = false").
		Order("created_at DESC").
		First(&handler).Error; err != nil {
		tx.Rollback()
		return &request_for_chain, err
	}

	function_info_for_handler = dtos.FunctionInfo{
		Name:  handler.Name,
		File:  handler.Path,
		Code:  handler.Code,
		Calls: []string{},
	}

	if err := tx.Model(&entities.Chain{}).
		Where("handler_id = ?", handler.ID).
		Find(&chains).Error; err != nil {
		tx.Rollback()
		return &request_for_chain, err
	}

	for _, chain := range chains {
		function_info_for_calls = append(function_info_for_calls, dtos.FunctionInfo{
			Name:  chain.Name,
			File:  chain.Path,
			Code:  chain.Code,
			Calls: []string{},
		})
	}

	request_for_chain = dtos.RequestForChain{
		Handler:   function_info_for_handler,
		CallChain: function_info_for_calls,
	}

	tx.Commit()

	return &request_for_chain, nil
}

func sendToOpenAI(req *dtos.RequestForChain) error {

	openaiClient := openai.NewLMStudioClient()

	openai_req := openai.ChatRequest{
		Model: "qwen/qwen2.5-vl-7b",
		Messages: []openai.ChatMessage{
			{
				Role:    "system",
				Content: prompt.GetPrompt("endpoint"),
			},
			{
				Role:    "user",
				Content: "Aşağıdaki kodu analiz et ve bana raporla. " + req.Handler.Code,
			},
		},
		Temperature: 0.3,
		MaxTokens:   10000,
	}

	resp, err := openaiClient.SendChatRequest(openai_req)
	if err != nil {
		return err
	}

	if len(resp.Choices) == 0 {
		return fmt.Errorf("yanıt bulunamadı")
	}

	log.Println("resp.Choices: ", resp.Choices[0])

	return nil
}
