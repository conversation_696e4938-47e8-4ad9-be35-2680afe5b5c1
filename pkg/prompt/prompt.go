package prompt

func GetPrompt(name string) string {
	if name == "endpoint" {
		return endpointSummaryPrompt()
	}
	if name == "general" {
		return generalSummaryPrompt()
	}

	return ""
}

func endpointSummaryPrompt() string {
	return `
		You are an analyst who summarizes what services written in the fintech field do. 
		Read and understand the given endpoint code. 
		Write a summary in one sentence (max 30 words). 
		Return only the summary text.
	`
}

func generalSummaryPrompt() string {
	return `
		You are an analyst who summarizes services in the fintech field. 
		Read and understand the given short summaries. 
		Then, write a general summary that explains what the entire project does (maximum 200 words). 
		Return only the summary text.
	`
}
