package chain

import (
	"context"

	"github.com/agent-summurizer/pkg/dtos"
	"github.com/agent-summurizer/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	SaveChain(ctx context.Context, req dtos.RequestForChain) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) SaveChain(ctx context.Context, req dtos.RequestForChain) error {

	var (
		handler entities.Handler
		chain   entities.Chain
	)

	handler = entities.Handler{
		Name: req.Handler.Name,
		Path: req.Handler.File,
		Code: req.Handler.Code,
	}

	tx := r.db.Begin()

	if err := tx.WithContext(ctx).
		Model(&entities.Handler{}).
		Create(&handler).Error; err != nil {
		tx.Rollback()
		return err
	}

	for _, call := range req.CallChain {
		chain = entities.Chain{
			HandlerID: handler.ID,
			Name:      call.Name,
			Path:      call.File,
			Code:      call.Code,
		}

		if err := tx.WithContext(ctx).
			Model(&entities.Chain{}).
			Create(&chain).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	tx.Commit()

	return nil
}
