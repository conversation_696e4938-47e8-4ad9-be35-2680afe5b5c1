package chain

import (
	"context"

	"github.com/agent-summurizer/pkg/dtos"
)

type Service interface {
	GetChain(ctx context.Context, req dtos.RequestForChain) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetChain(ctx context.Context, req dtos.RequestForChain) error {

	return s.repository.SaveChain(ctx, req)
}
