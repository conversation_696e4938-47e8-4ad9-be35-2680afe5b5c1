package entities

import "time"

type Handler struct {
	Base
	Name        string    `json:"name" example:"homeGroup"`
	Path        string    `json:"path" example:"app/api/routes/other.go"`
	Code        string    `json:"code" example:"func (h *Handler) GetHomeGroup(c *gin.Context) {}"`
	IsProcessed bool      `json:"is_processed" example:"false"`
	ProcessedAt time.Time `json:"processed_at" example:"1678953600"`
}
