# LM Studio Entegrasyonu

Bu dokümantasyon, projedeki LM Studio entegrasyonunun nasıl kullanılacağını açıklar.

## Kurulum

### 1. LM Studio Kurulumu
- [LM Studio](https://lmstudio.ai/) indirin ve kurun
- Bir model indirin (örn: Llama 2, Mistral, vb.)
- Local Server'ı başlatın (varsayılan: `http://localhost:1234`)

### 2. Go Modülü
Proje zaten gerekli yapıları içeriyor. Ek paket kurulumuna gerek yok.

## Kullanım

### Basit Kullanım

```go
package main

import (
    "fmt"
    "log"
    "github.com/agent-summurizer/pkg/prompt"
)

func main() {
    // İstemci oluştur
    client := prompt.NewLMStudioClient("http://localhost:1234/v1")
    
    // Basit sohbet
    response, err := client.SimpleChat("Merhaba!")
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Println("AI:", response)
}
```

### Detaylı <PERSON>llanım

```go
// Özelleştirilmiş istek
chatReq := prompt.ChatRequest{
    Model: "local-model",
    Messages: []prompt.ChatMessage{
        {
            Role:    "system",
            Content: "Sen yardımcı bir asistansın.",
        },
        {
            Role:    "user", 
            Content: "Go programlama hakkında bilgi ver.",
        },
    },
    Temperature: 0.7,
    MaxTokens:   500,
}

response, err := client.SendChatRequest(chatReq)
```

## Yapılandırma

### LM Studio Ayarları
1. LM Studio'yu açın
2. Bir model yükleyin
3. "Local Server" sekmesine gidin
4. "Start Server" butonuna tıklayın
5. Port numarasını kontrol edin (varsayılan: 1234)

### Go Kodu Ayarları

```go
// Farklı port kullanmak için
client := prompt.NewLMStudioClient("http://localhost:8080/v1")

// Varsayılan ayarlar (localhost:1234)
client := prompt.NewLMStudioClient("")
```

## API Referansı

### LMStudioClient

#### NewLMStudioClient(baseURL string) *LMStudioClient
Yeni LM Studio istemcisi oluşturur.

**Parametreler:**
- `baseURL`: LM Studio server adresi (boş bırakılırsa localhost:1234 kullanılır)

#### SimpleChat(message string) (string, error)
Basit sohbet fonksiyonu.

**Parametreler:**
- `message`: Gönderilecek mesaj

**Dönüş:**
- `string`: AI yanıtı
- `error`: Hata durumu

#### SendChatRequest(req ChatRequest) (*ChatResponse, error)
Detaylı sohbet isteği gönderir.

### Yapılar

#### ChatMessage
```go
type ChatMessage struct {
    Role    string `json:"role"`    // "system", "user", "assistant"
    Content string `json:"content"` // Mesaj içeriği
}
```

#### ChatRequest
```go
type ChatRequest struct {
    Model       string        `json:"model"`
    Messages    []ChatMessage `json:"messages"`
    Temperature float64       `json:"temperature,omitempty"` // 0.0-2.0
    MaxTokens   int           `json:"max_tokens,omitempty"`
    Stream      bool          `json:"stream,omitempty"`
}
```

## Örnek Kullanımlar

### 1. Basit Soru-Cevap
```go
client := prompt.NewLMStudioClient("")
answer, _ := client.SimpleChat("2+2 kaç eder?")
fmt.Println(answer)
```

### 2. Sistem Promptu ile
```go
req := prompt.ChatRequest{
    Model: "local-model",
    Messages: []prompt.ChatMessage{
        {Role: "system", Content: "Sen bir matematik öğretmenisin."},
        {Role: "user", Content: "Calculus nedir?"},
    },
}
```

### 3. Sohbet Geçmişi
```go
messages := []prompt.ChatMessage{
    {Role: "user", Content: "Merhaba"},
    {Role: "assistant", Content: "Merhaba! Nasıl yardımcı olabilirim?"},
    {Role: "user", Content: "Go programlama öğrenmek istiyorum"},
}
```

## Hata Yönetimi

```go
response, err := client.SimpleChat("test")
if err != nil {
    switch {
    case strings.Contains(err.Error(), "connection refused"):
        log.Println("LM Studio çalışmıyor olabilir")
    case strings.Contains(err.Error(), "timeout"):
        log.Println("İstek zaman aşımına uğradı")
    default:
        log.Printf("Bilinmeyen hata: %v", err)
    }
}
```

## Test Etme

Örnek dosyayı çalıştırmak için:

```bash
# LM Studio'nun çalıştığından emin olun
go run examples/lm_studio_example.go
```

## Sorun Giderme

1. **Connection Refused**: LM Studio server'ının çalıştığından emin olun
2. **Model Not Found**: LM Studio'da bir model yüklendiğinden emin olun
3. **Timeout**: Model yanıt vermesi uzun sürüyorsa timeout değerini artırın
4. **Port Hatası**: LM Studio'nun hangi portta çalıştığını kontrol edin
