app:
  name: agent-summurizer
  port: 8000
  host:
  jwt_issuer: "agent-summurizer"
  jwt_secret: "secret"
  client_id: xxx
  onesignal_api_key: 123456
  force_update_key: xxx
redis:
  host: agent-summurizer-redis
  port: 6379
  pass: xxx
database:
  host: agent-summurizer-db
  port: 5432
  user: xxx
  pass: xxx
  name: agent_summurizer
cloudinary:
  name: xxx
  api_key: xxx
  api_secret: xxx
  api_folder: xxx
allows:
  methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS
  headers:
  - Content-Type
  - Authorization
  - X-CSRF-Token
  - data-api-key
  origins:
    - http://localhost:8000
    - http://localhost:9000
    - http://localhost:4040
    - http://localhost:3000