# Agent Summarizer

Go tabanlı agent summarizer projesi.

## Özellikler

- LM Studio entegrasyonu
- RESTful API
- Redis cache
- PostgreSQL veritabanı
- Docker desteği
- Swagger dokümantasyonu

## LM Studio Entegrasyonu

Bu proje LM Studio ile entegre çalışabilir. Detaylı kullanım için [LM Studio Dokümantasyonu](docs/LM_STUDIO_USAGE.md) dosyasına bakın.

### Hızlı <PERSON>langıç

```go
import "github.com/agent-summurizer/pkg/prompt"

// LM Studio istemcisi oluştur
client := prompt.NewLMStudioClient("http://localhost:1234/v1")

// Basit sohbet
response, err := client.SimpleChat("Merhaba!")
if err != nil {
    log.Fatal(err)
}
fmt.Println("AI:", response)
```

## Kurulum

```bash
# Bağımlılıkları yükle
go mod tidy

# Uygulamayı çalıştır
go run main.go
```

## Docker ile Çalıştırma

```bash
# Development ortamı
docker-compose -f docker-compose-hot.yml up

# Production build
docker build -f Dockerfile.dev -t agent-summarizer .
```

## API Dokümantasyonu

Swagger UI: `http://localhost:8080/swagger/index.html`

## Proje Yapısı

```
├── app/                 # Uygulama katmanı
├── pkg/                 # Paketler
│   ├── prompt/         # LM Studio entegrasyonu
│   ├── config/         # Konfigürasyon
│   ├── database/       # Veritabanı
│   └── ...
├── docs/               # Dokümantasyon
├── examples/           # Örnek kullanımlar
└── main.go            # Ana dosya
```