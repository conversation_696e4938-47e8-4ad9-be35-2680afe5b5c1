version: "3"
services:
  agent-summurizer-db:
    image: "postgres:14.6"
    container_name: agent-summurizer-db
    volumes:
      - agent_summurizer_db_data:/var/lib/postgresql/data
    networks:
      - main
    restart: always
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - TZ="Europe/Istanbul"

  agent-summurizer:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: agent-summurizer
    environment:
      - TZ="Europe/Istanbul"
    container_name: agent-summurizer
    restart: always
    networks:
      - main
    volumes:
      - ./:/app
      - ./config-hot.yaml:/app/config.yaml
    ports:
      - 8000:8000
    depends_on:
      - agent-summurizer-db
      - agent-summurizer-redis
  
  agent-summurizer-redis:
    image: "redis:latest"
    container_name: agent-summurizer-redis
    networks:
      - main
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}

volumes:
  agent_summurizer_db_data:

networks:
  main:
    name: main_network
    driver: bridge