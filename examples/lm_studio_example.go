package main

import (
	"fmt"
	"log"

	"github.com/agent-summurizer/pkg/openai"
)

func main() {
	// LM Studio istemcisi oluştur
	client := openai.NewLMStudioClient()

	// Basit sohbet örneği
	fmt.Println("=== Basit Sohbet Örneği ===")
	response, err := client.SimpleChat("Merhaba! Nasılsın?")
	if err != nil {
		log.Printf("Hata: %v", err)
	} else {
		fmt.Printf("AI Yanıtı: %s\n", response)
	}

	// Daha detaylı istek örneği
	fmt.Println("\n=== Detaylı İstek Örneği ===")
	chatReq := openai.ChatRequest{
		Model: "local-model",
		Messages: []openai.ChatMessage{
			{
				Role:    "system",
				Content: "Sen yardımcı bir asistansın. Türkçe yanıt ver.",
			},
			{
				Role:    "user",
				Content: "Go programlama dili hakkında kısa bilgi verir misin?",
			},
		},
		Temperature: 0.7,
		MaxTokens:   500,
	}

	detailedResponse, err := client.SendChatRequest(chatReq)
	if err != nil {
		log.Printf("Hata: %v", err)
	} else {
		if len(detailedResponse.Choices) > 0 {
			fmt.Printf("AI Yanıtı: %s\n", detailedResponse.Choices[0].Message.Content)
			fmt.Printf("Kullanılan Token: %d\n", detailedResponse.Usage.TotalTokens)
		}
	}

	// Çoklu mesaj örneği (sohbet geçmişi)
	fmt.Println("\n=== Çoklu Mesaj Örneği ===")
	conversationReq := openai.ChatRequest{
		Model: "local-model",
		Messages: []openai.ChatMessage{
			{
				Role:    "system",
				Content: "Sen bir programlama uzmanısın.",
			},
			{
				Role:    "user",
				Content: "Go'da HTTP server nasıl oluşturulur?",
			},
			{
				Role:    "assistant",
				Content: "Go'da HTTP server oluşturmak için net/http paketini kullanabilirsin...",
			},
			{
				Role:    "user",
				Content: "Peki middleware nasıl eklenir?",
			},
		},
		Temperature: 0.5,
		MaxTokens:   300,
	}

	convResponse, err := client.SendChatRequest(conversationReq)
	if err != nil {
		log.Printf("Hata: %v", err)
	} else {
		if len(convResponse.Choices) > 0 {
			fmt.Printf("AI Yanıtı: %s\n", convResponse.Choices[0].Message.Content)
		}
	}
}
