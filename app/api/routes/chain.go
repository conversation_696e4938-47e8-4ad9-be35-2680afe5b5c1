package routes

import (
	"github.com/agent-summurizer/pkg/domains/chain"
	"github.com/agent-summurizer/pkg/dtos"
	"github.com/agent-summurizer/pkg/localizer"
	"github.com/agent-summurizer/pkg/middleware"
	"github.com/agent-summurizer/pkg/state"
	"github.com/gin-gonic/gin"
)

func ChainRoutes(r *gin.RouterGroup, s chain.Service) {
	r.GET("/get", middleware.FromClient(), getChain(s))

}

// @Summary summary for x
// @Description  lorem ipsum dolor sit amet
// @Tags X Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path string true "x"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Router /x [GET]
func getChain(s chain.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForChain
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		if err := s.GetChain(c, req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   "x",
			"status": 201,
		})
	}
}

func getSummary(s chain.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// var req dtos.RequestForSummary
		// if err := c.ShouldBindJSON(&req); err != nil {
		// 	c.AbortWithStatusJSON(400, gin.H{
		// 		"error":  localizer.GetTranslated("error_should_bind_json", state.GetCurrentPhoneLanguage(c), nil),
		// 		"status": 400,
		// 	})
		// 	return
		// }

		c.JSON(201, gin.H{
			"data":   "x",
			"status": 201,
		})
	}
}
