package cmd

import (
	"github.com/agent-summurizer/pkg/cache"
	"github.com/agent-summurizer/pkg/config"
	"github.com/agent-summurizer/pkg/cron"
	"github.com/agent-summurizer/pkg/database"
	"github.com/agent-summurizer/pkg/server"
)

func StartApp() {
	config := config.InitConfig()
	database.InitDB(config.Database)
	cache.InitRedis(config.Redis)
	cron.MyCron()
	server.LaunchHttpServer(config.App, config.Allows)
}
